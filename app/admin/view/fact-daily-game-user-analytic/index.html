
<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="utf-8">
        <title>浏览页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
        <style>
            /* Filter button styling */
            .layui-btn-container .pear-btn {
                margin-right: 10px;
            }
            .layui-btn-container .pear-btn.filter-selected {
                background-color: #28a745 !important;
                border-color: #28a745 !important;
                color: white !important;
            }
            .layui-btn-container .pear-btn.filter-unselected {
                background-color: white !important;
                border-color: #ddd !important;
                color: #333 !important;
            }
        </style>
    </head>
    <body class="pear-container">
    
        <!-- 顶部查询表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form top-search-from">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">统计日期</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block" id="date">
                                <input type="text" autocomplete="off" name="date[]" id="date-date-start" class="layui-input inline-block" placeholder="开始时间">
                                -
                                <input type="text" autocomplete="off" name="date[]" id="date-date-end" class="layui-input inline-block" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">渠道</label>
                        <div class="layui-input-block">
                            <div name="channel_id" id="channel_id" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">游戏</label>
                        <div class="layui-input-block">
                            <div name="game_id" id="game_id" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-inline">
                        <label class="layui-form-label"></label>
                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="table-query">
                            <i class="layui-icon layui-icon-search"></i>查询
                        </button>
                        <button type="reset" class="pear-btn pear-btn-md" lay-submit lay-filter="table-reset">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                    <div class="toggle-btn">
                        <a class="layui-hide">展开<i class="layui-icon layui-icon-down"></i></a>
                        <a class="layui-hide">收起<i class="layui-icon layui-icon-up"></i></a>
                    </div>
                </form>
            </div>
        </div>


        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="data-table" lay-filter="data-table"></table>
            </div>
        </div>

        <!-- 表格顶部工具栏 -->
        <script type="text/html" id="table-toolbar">
            <div class="layui-btn-container">
                <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="active">活跃用户</button>
                <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="new">新增用户</button>
                <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="returning">老用户</button>
            </div>
        </script>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        <script src="/app/admin/admin/js/common.js"></script>
        
        <script>

            // 相关常量
            const PRIMARY_KEY = "date";
            const SELECT_API = "/admin/fact-daily-game-user-analytic/select";
            const UPDATE_API = "/admin/fact-daily-game-user-analytic/update";
            const DELETE_API = "/admin/fact-daily-game-user-analytic/delete";
            const INSERT_URL = "/admin/fact-daily-game-user-analytic/insert";
            const UPDATE_URL = "/admin/fact-daily-game-user-analytic/update";
            
            // 字段 统计日期 date
            layui.use(["laydate"], function() {
                layui.laydate.render({
                    elem: "#date",
                    range: ["#date-date-start", "#date-date-end"],
            
                });
            })
            
            // 字段 渠道 channel_id
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.$.ajax({
                    url: "/admin/channel/select?format=select",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#channel_id").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#channel_id",
                            name: "channel_id",
                            initValue: initValue,
                            filterable: true,
                            data: res.data, 
                            model: {"icon":"hidden","label":{"type":"text"}},
                            clickClose: true,
                            radio: true,
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            // 字段 游戏 game_id
            layui.use(["jquery", "xmSelect", "popup"], function() {
                layui.$.ajax({
                    url: "/admin/game/select?format=select",
                    dataType: "json",
                    success: function (res) {
                        let value = layui.$("#game_id").attr("value");
                        let initValue = value ? value.split(",") : [];
                        layui.xmSelect.render({
                            el: "#game_id",
                            name: "game_id",
                            initValue: initValue,
                            filterable: true,
                            data: res.data, 
                            model: {"icon":"hidden","label":{"type":"text"}},
                            clickClose: true,
                            radio: true,
                        });
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                    }
                });
            });
            
            // 表格渲染
            layui.use(["table", "form", "common", "popup", "util"], function() {
                let table = layui.table;
                let form = layui.form;
                let $ = layui.$;
                let common = layui.common;
                let util = layui.util;

                // 定义列筛选映射 - 使用排除逻辑（定义要隐藏的列）
                const columnFilters = {
                    // 活跃用户筛选：隐藏新增用户和老用户相关列
                    active: [
                        // 隐藏新增用户相关列
                        "daily_new_users", "new_users_sessions", "new_users_duration_seconds",
                        "new_users_avg_session_duration", "new_users_avg_sessions_per_user",
                        "new_users_day_1_retention_rate", "new_users_day_3_retention_rate",
                        "new_users_day_7_retention_rate", "new_users_day_30_retention_rate",
                        // 隐藏老用户相关列
                        "active_users_count", "active_users_sessions", "active_users_duration_seconds",
                        "active_users_avg_session_duration", "active_users_avg_sessions_per_user",
                        "active_users_day_1_retention_rate", "active_users_day_3_retention_rate",
                        "active_users_day_7_retention_rate", "active_users_day_30_retention_rate"
                    ],
                    // 新增用户筛选：隐藏活跃用户和老用户相关列
                    new: [
                        // 隐藏总体活跃用户相关列
                        "daily_active_users", "total_sessions", "total_session_duration_seconds",
                        "avg_session_duration_seconds", "avg_sessions_per_user",
                        "day_1_retention_rate", "day_3_retention_rate", "day_7_retention_rate", "day_30_retention_rate",
                        // 隐藏老用户相关列
                        "active_users_count", "active_users_sessions", "active_users_duration_seconds",
                        "active_users_avg_session_duration", "active_users_avg_sessions_per_user",
                        "active_users_day_1_retention_rate", "active_users_day_3_retention_rate",
                        "active_users_day_7_retention_rate", "active_users_day_30_retention_rate"
                    ],
                    // 老用户筛选：隐藏活跃用户和新增用户相关列
                    returning: [
                        // 隐藏总体活跃用户相关列
                        "daily_active_users", "total_sessions", "total_session_duration_seconds",
                        "avg_session_duration_seconds", "avg_sessions_per_user",
                        "day_1_retention_rate", "day_3_retention_rate", "day_7_retention_rate", "day_30_retention_rate",
                        // 隐藏新增用户相关列
                        "daily_new_users", "new_users_sessions", "new_users_duration_seconds",
                        "new_users_avg_session_duration", "new_users_avg_sessions_per_user",
                        "new_users_day_1_retention_rate", "new_users_day_3_retention_rate",
                        "new_users_day_7_retention_rate", "new_users_day_30_retention_rate"
                    ]
                };

                // 当前筛选状态
                let currentFilter = 'active';

                // 应用列筛选函数 - 使用排除逻辑，无闪烁版本
                function applyColumnFilter(filterType, skipReload) {
                    currentFilter = filterType;
                    const hiddenFields = columnFilters[filterType];

                    // 如果是初始渲染，更新cols数组并跳过动态隐藏
                    if (skipReload) {
                        cols.forEach(function(col) {
                            col.hide = hiddenFields.includes(col.field);
                        });
                        updateFilterButtonStates(filterType);
                        return;
                    }

                    // 使用 table.hideCol 方法动态隐藏/显示列，避免表格闪烁
                    const hideColsArray = [];

                    // 遍历所有列，构建隐藏/显示的配置数组
                    cols.forEach(function(col) {
                        if (col.field) { // 只处理有field的列
                            const shouldHide = hiddenFields.includes(col.field);
                            hideColsArray.push({
                                field: col.field,
                                hide: shouldHide
                            });
                            // 同步更新cols数组的hide属性
                            col.hide = shouldHide;
                        }
                    });

                    // 批量设置列的显示/隐藏状态
                    if (hideColsArray.length > 0) {
                        table.hideCol("data-table", hideColsArray);
                    }

                    // 更新筛选按钮状态
                    updateFilterButtonStates(filterType);
                }

                // 更新筛选按钮状态
                function updateFilterButtonStates(activeFilter) {
                    // 移除所有按钮的选中状态
                    $(".layui-btn-container .pear-btn").removeClass("filter-selected").addClass("filter-unselected");

                    // 为当前选中的按钮添加选中状态
                    $(".layui-btn-container .pear-btn[lay-event='" + activeFilter + "']").removeClass("filter-unselected").addClass("filter-selected");
                }

				// 表头参数
				let cols = [
					{
						title: "统计日期",align: "center",
						field: "date",
                        fixed: "left",
                        width: 120,
						sort: true,
					},{
						title: "渠道",align: "center",
						field: "channel_id",
                        fixed: "left",
                        width: 120,
						sort: true,
						templet: function (d) {
							let field = "channel_id";
							if (typeof d[field] == "undefined") return "";
							let items = [];
							layui.each((d[field] + "").split(","), function (k , v) {
								items.push(apiResults[field][v] || v);
							});
							return util.escape(items.join(","));
						}
					},{
						title: "游戏",align: "center",
						field: "game_id",
                        fixed: "left",
                        width: 120,
						sort: true,
						templet: function (d) {
							let field = "game_id";
							if (typeof d[field] == "undefined") return "";
							let items = [];
							layui.each((d[field] + "").split(","), function (k , v) {
								items.push(apiResults[field][v] || v);
							});
							return util.escape(items.join(","));
						}
					},{
						title: "活跃用户数",align: "center",
						field: "daily_active_users",
                        width: 120
					},{
						title: "活跃用户总访问次数",align: "center",
						field: "total_sessions",
                        width: 120
					},{
						title: "活跃用户总停留时长",align: "center",
						field: "total_session_duration_seconds",
                        width: 120,
						templet: function (d) {
							if (typeof d.total_session_duration_seconds == "undefined" || d.total_session_duration_seconds === null) return "";
							let seconds = parseFloat(d.total_session_duration_seconds);
							let minutes = Math.round(seconds / 60 * 100) / 100; // 保留2位小数
							return minutes + " 分钟";
						}
					},{
						title: "活跃用户平均停留时长",align: "center",
						field: "avg_session_duration_seconds",
                        width: 120,
						templet: function (d) {
							if (typeof d.avg_session_duration_seconds == "undefined" || d.avg_session_duration_seconds === null) return "";
							let seconds = parseFloat(d.avg_session_duration_seconds);
							let minutes = Math.round(seconds / 60 * 100) / 100; // 保留2位小数
							return minutes + " 分钟";
						}
					},{
						title: "活跃用户人均访问次数",align: "center",
						field: "avg_sessions_per_user",
                        width: 120
					},{
						title: "活跃用户总心跳次数",align: "center",
						field: "total_heartbeats",
                        width: 120,
						hide: true,
					},{
						title: "活跃用户平均每会话心跳次数",align: "center",
						field: "avg_heartbeats_per_session",
                        width: 200,
						hide: true,
					},{
						title: "老用户数",align: "center",
						field: "active_users_count",
                        width: 200,
						hide: true,
					},{
						title: "老用户总访问次数",align: "center",
						field: "active_users_sessions",
                        width: 200,
						hide: true,
					},{
						title: "老用户总访问时长",align: "center",
						field: "active_users_duration_seconds",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.active_users_duration_seconds == "undefined" || d.active_users_duration_seconds === null) return "";
							let seconds = parseFloat(d.active_users_duration_seconds);
							let minutes = Math.round(seconds / 60 * 100) / 100; // 保留2位小数
							return minutes + " 分钟";
						}
					},{
						title: "老用户平均停留时长",align: "center",
						field: "active_users_avg_session_duration",
                        width: 200,
						hide: true,
                        templet: function (d) {
							if (typeof d.active_users_avg_session_duration == "undefined" || d.active_users_avg_session_duration === null) return "";
							let seconds = parseFloat(d.active_users_avg_session_duration);
							let minutes = Math.round(seconds / 60 * 100) / 100; // 保留2位小数
							return minutes + " 分钟";
						}
					},{
						title: "老用户平均访问次数",align: "center",
						field: "active_users_avg_sessions_per_user",
                        width: 200,
						hide: true,
					},{
						title: "新增用户数",align: "center",
						field: "daily_new_users",
                        width: 120,
                        hide: true,
					},{
						title: "新增用户总访问次数",align: "center",
						field: "new_users_sessions",
                        width: 200,
						hide: true,
					},{
						title: "新增用户总访问时长",align: "center",
						field: "new_users_duration_seconds",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.new_users_duration_seconds == "undefined" || d.new_users_duration_seconds === null) return "";
							let seconds = parseFloat(d.new_users_duration_seconds);
							let minutes = Math.round(seconds / 60 * 100) / 100; // 保留2位小数
							return minutes + " 分钟";
						}
					},{
						title: "新增用户平均停留时长",align: "center",
						field: "new_users_avg_session_duration",
                        width: 200,
						hide: true,
                        templet: function (d) {
							if (typeof d.new_users_avg_session_duration == "undefined" || d.new_users_avg_session_duration === null) return "";
							let seconds = parseFloat(d.new_users_avg_session_duration);
							let minutes = Math.round(seconds / 60 * 100) / 100; // 保留2位小数
							return minutes + " 分钟";
						}
					},{
						title: "新增用户平均访问次数",align: "center",
						field: "new_users_avg_sessions_per_user",
                        width: 200,
						hide: true,
					},{
						title: "活跃用户次日留存率",align: "center",
						field: "day_1_retention_rate",
                        width: 150,
						templet: function (d) {
							if (typeof d.day_1_retention_rate == "undefined" || d.day_1_retention_rate === null) return "";
							let rate = parseFloat(d.day_1_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "活跃用户3日留存率",align: "center",
						field: "day_3_retention_rate",
                        width: 150,
						templet: function (d) {
							if (typeof d.day_3_retention_rate == "undefined" || d.day_3_retention_rate === null) return "";
							let rate = parseFloat(d.day_3_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "活跃用户7日留存率",align: "center",
						field: "day_7_retention_rate",
                        width: 150,
						templet: function (d) {
							if (typeof d.day_7_retention_rate == "undefined" || d.day_7_retention_rate === null) return "";
							let rate = parseFloat(d.day_7_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "活跃用户30日留存率",align: "center",
						field: "day_30_retention_rate",
                        width: 150,
						templet: function (d) {
							if (typeof d.day_30_retention_rate == "undefined" || d.day_30_retention_rate === null) return "";
							let rate = parseFloat(d.day_30_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "新增用户次日留存率",align: "center",
						field: "new_users_day_1_retention_rate",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.new_users_day_1_retention_rate == "undefined" || d.new_users_day_1_retention_rate === null) return "";
							let rate = parseFloat(d.new_users_day_1_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "新增用户3日留存率",align: "center",
						field: "new_users_day_3_retention_rate",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.new_users_day_3_retention_rate == "undefined" || d.new_users_day_3_retention_rate === null) return "";
							let rate = parseFloat(d.new_users_day_3_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "新增用户7日留存率",align: "center",
						field: "new_users_day_7_retention_rate",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.new_users_day_7_retention_rate == "undefined" || d.new_users_day_7_retention_rate === null) return "";
							let rate = parseFloat(d.new_users_day_7_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "新增用户30日留存率",align: "center",
						field: "new_users_day_30_retention_rate",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.new_users_day_30_retention_rate == "undefined" || d.new_users_day_30_retention_rate === null) return "";
							let rate = parseFloat(d.new_users_day_30_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "老用户次日留存率",align: "center",
						field: "active_users_day_1_retention_rate",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.active_users_day_1_retention_rate == "undefined" || d.active_users_day_1_retention_rate === null) return "";
							let rate = parseFloat(d.active_users_day_1_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "老用户3日留存率",align: "center",
						field: "active_users_day_3_retention_rate",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.active_users_day_3_retention_rate == "undefined" || d.active_users_day_3_retention_rate === null) return "";
							let rate = parseFloat(d.active_users_day_3_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "老用户7日留存率",align: "center",
						field: "active_users_day_7_retention_rate",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.active_users_day_7_retention_rate == "undefined" || d.active_users_day_7_retention_rate === null) return "";
							let rate = parseFloat(d.active_users_day_7_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "老用户30日留存率",align: "center",
						field: "active_users_day_30_retention_rate",
                        width: 200,
						hide: true,
						templet: function (d) {
							if (typeof d.active_users_day_30_retention_rate == "undefined" || d.active_users_day_30_retention_rate === null) return "";
							let rate = parseFloat(d.active_users_day_30_retention_rate);
							return (rate * 100).toFixed(2) + "%";
						}
					},{
						title: "创建时间",align: "center",
						field: "created_at",
					},{
						title: "更新时间",align: "center",
						field: "updated_at",
					}
				];
				
				// 渲染表格
				function render()
				{
				    // 应用当前筛选状态到列（跳过重载，因为我们正在初始渲染）
				    applyColumnFilter(currentFilter, true);

				    table.render({
				        elem: "#data-table",
				        url: SELECT_API,
				        page: true,
				        cols: [cols],
				        skin: "line",
				        size: "lg",
				        toolbar: "#table-toolbar",
				        autoSort: false,
				        defaultToolbar: [{
				            title: "刷新",
				            layEvent: "refresh",
				            icon: "layui-icon-refresh",
				        }, "filter", "print", "exports"],
				        done: function () {
				            layer.photos({photos: 'div[lay-id="data-table"]', anim: 5});
				            // 初始化筛选按钮状态
				            updateFilterButtonStates(currentFilter);
				        }
				    });
				}
				
				// 获取表格中下拉或树形组件数据
				let apis = [];
				apis.push(["channel_id", "/admin/channel/select?format=select"]);
				apis.push(["game_id", "/admin/game/select?format=select"]);
				let apiResults = {};
				apiResults["channel_id"] = [];
				apiResults["game_id"] = [];
				let count = apis.length;
				layui.each(apis, function (k, item) {
				    let [field, url] = item;
				    $.ajax({
				        url: url,
				        dateType: "json",
				        success: function (res) {
				            if (res.code) {
				                return layui.popup.failure(res.msg);
				            }
				            function travel(items) {
				                for (let k in items) {
				                    let item = items[k];
				                    apiResults[field][item.value] = item.name;
				                    if (item.children) {
				                        travel(item.children);
				                    }
				                }
				            }
				            travel(res.data);
				        },
				        complete: function () {
				            if (--count === 0) {
				                render();
				            }
				        }
				    });
				});
				if (!count) {
				    render();
				}
				
                // 编辑或删除行事件
                table.on("tool(data-table)", function(obj) {
                    if (obj.event === "remove") {
                        remove(obj);
                    } else if (obj.event === "edit") {
                        edit(obj);
                    }
                });

                // 表格顶部工具栏事件
                table.on("toolbar(data-table)", function(obj) {
                    if (obj.event === "add") {
                        add();
                    } else if (obj.event === "refresh") {
                        refreshTable();
                    } else if (obj.event === "batchRemove") {
                        batchRemove(obj);
                    } else if (obj.event === "active" || obj.event === "new" || obj.event === "returning") {
                        // 处理筛选按钮事件
                        applyColumnFilter(obj.event);
                    }
                });

                // 表格顶部搜索事件
                form.on("submit(table-query)", function(data) {
                    table.reload("data-table", {
                        page: {
                            curr: 1
                        },
                        where: data.field
                    })
                    return false;
                });
                
                // 表格顶部搜索重置事件
                form.on("submit(table-reset)", function(data) {
                    table.reload("data-table", {
                        where: []
                    })
                });
                
                // 字段允许为空
                form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });

                // 表格排序事件
                table.on("sort(data-table)", function(obj){
                    table.reload("data-table", {
                        initSort: obj,
                        scrollPos: "fixed",
                        where: {
                            field: obj.field,
                            order: obj.type
                        }
                    });
                });

                // 表格新增数据
                let add = function() {
                    layer.open({
                        type: 2,
                        title: "新增",
                        shade: 0.1,
                        maxmin: true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: INSERT_URL
                    });
                }

                // 表格编辑数据
                let edit = function(obj) {
                    let value = obj.data[PRIMARY_KEY];
                    layer.open({
                        type: 2,
                        title: "修改",
                        shade: 0.1,
                        maxmin: true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: UPDATE_URL + "?" + PRIMARY_KEY + "=" + value
                    });
                }

                // 删除一行
                let remove = function(obj) {
                    return doRemove(obj.data[PRIMARY_KEY]);
                }

                // 删除多行
                let batchRemove = function(obj) {
                    let checkIds = common.checkField(obj, PRIMARY_KEY);
                    if (checkIds === "") {
                        layui.popup.warning("未选中数据");
                        return false;
                    }
                    doRemove(checkIds.split(","));
                }

                // 执行删除
                let doRemove = function (ids) {
                    let data = {};
                    data[PRIMARY_KEY] = ids;
                    layer.confirm("确定删除?", {
                        icon: 3,
                        title: "提示"
                    }, function(index) {
                        layer.close(index);
                        let loading = layer.load();
                        $.ajax({
                            url: DELETE_API,
                            data: data,
                            dataType: "json",
                            type: "post",
                            success: function(res) {
                                layer.close(loading);
                                if (res.code) {
                                    return layui.popup.failure(res.msg);
                                }
                                return layui.popup.success("操作成功", refreshTable);
                            }
                        })
                    });
                }

                // 刷新表格数据
                window.refreshTable = function() {
                    table.reloadData("data-table", {
                        scrollPos: "fixed",
                        done: function (res, curr) {
                            if (curr > 1 && res.data && !res.data.length) {
                                curr = curr - 1;
                                table.reloadData("data-table", {
                                    page: {
                                        curr: curr
                                    },
                                })
                            }
                        }
                    });
                }


            })

        </script>
    </body>
</html>
